# Lean Tactic Energy Function Project Configuration

# 大模型配置
llm:
  provider: "openai"  # 可选: openai, anthropic, local
  model: "gpt-4"
  api_key: null  # 从环境变量获取
  max_tokens: 2048
  temperature: 0.7

# 数据配置
data:
  mathlib_path: "data/mathlib_sample"
  max_theorems: 1000
  max_proof_steps: 50
  
# Tactic配置
tactics:
  supported_tactics:
    - "rw"
    - "simp"
    - "apply"
    - "exact"
    - "intro"
    - "cases"
    - "induction"
    - "ring"
    - "linarith"
    - "norm_num"
  
# 能量函数配置
energy:
  function_type: "neural"  # 可选: neural, symbolic, hybrid
  hidden_dims: [256, 128, 64]
  learning_rate: 0.001
  
# 评估配置
evaluation:
  test_split: 0.2
  validation_split: 0.1
  metrics:
    - "accuracy"
    - "mean_energy"
    - "energy_variance"
    
# 日志配置
logging:
  level: "INFO"
  file: "logs/lean_tactic.log"
