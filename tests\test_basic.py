"""
基础测试 - 测试项目的核心功能
"""

import unittest
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.data.lean_simulator import LeanDataSimulator, ProofState, TacticApplication
from src.tactics.tactic_types import TacticGenerator, TacticContext, tactic_registry, TacticCandidate
from src.models.llm_interface import MockLLMInterface, create_llm_manager
from src.energy.energy_constructor import EnergyFunctionConstructor, EnergyFunctionSpec, EnergyFunction


class TestLeanSimulator(unittest.TestCase):
    """测试Lean数据模拟器"""
    
    def setUp(self):
        self.simulator = LeanDataSimulator()
    
    def test_generate_proof_state(self):
        """测试证明状态生成"""
        state = self.simulator.generate_proof_state(complexity=3)
        
        self.assertIsInstance(state, ProofState)
        self.assertIsInstance(state.goals, list)
        self.assertIsInstance(state.hypotheses, list)
        self.assertIsInstance(state.context, dict)
        self.assertGreaterEqual(len(state.goals), 1)
    
    def test_generate_theorem(self):
        """测试定理生成"""
        theorem = self.simulator.generate_theorem(1)
        
        self.assertIsNotNone(theorem.name)
        self.assertIsNotNone(theorem.statement)
        self.assertIsInstance(theorem.proof_steps, list)
        self.assertGreater(len(theorem.proof_steps), 0)
        self.assertIn(theorem.difficulty, range(1, 11))
    
    def test_generate_dataset(self):
        """测试数据集生成"""
        theorems = self.simulator.generate_dataset(5)
        
        self.assertEqual(len(theorems), 5)
        for theorem in theorems:
            self.assertIsNotNone(theorem.name)
            self.assertIsInstance(theorem.proof_steps, list)


class TestTacticSystem(unittest.TestCase):
    """测试Tactic系统"""
    
    def setUp(self):
        self.generator = TacticGenerator(tactic_registry)
    
    def test_tactic_registry(self):
        """测试tactic注册表"""
        tactics = tactic_registry.list_all_tactics()
        
        self.assertIn("rw", tactics)
        self.assertIn("simp", tactics)
        self.assertIn("apply", tactics)
        
        rw_tactic = tactic_registry.get_tactic("rw")
        self.assertIsNotNone(rw_tactic)
        self.assertEqual(rw_tactic.name, "rw")
    
    def test_generate_candidates(self):
        """测试候选tactic生成"""
        context = TacticContext(
            goal_type="a + b = b + a",
            available_hypotheses=["h1: a = b"],
            available_variables=["a", "b"],
            proof_depth=1,
            previous_tactics=[]
        )
        
        candidates = self.generator.generate_candidates(context, max_candidates=5)
        
        self.assertIsInstance(candidates, list)
        self.assertLessEqual(len(candidates), 5)
        
        for candidate in candidates:
            self.assertIsInstance(candidate, TacticCandidate)
            self.assertIsInstance(candidate.confidence, float)
            self.assertGreaterEqual(candidate.confidence, 0)
            self.assertLessEqual(candidate.confidence, 1)


class TestLLMInterface(unittest.TestCase):
    """测试大模型接口"""
    
    def setUp(self):
        self.interface = MockLLMInterface()
    
    def test_mock_interface(self):
        """测试模拟接口"""
        response = self.interface.generate("Test prompt")
        
        self.assertTrue(response.success)
        self.assertIsInstance(response.content, str)
        self.assertGreater(len(response.content), 0)
    
    def test_energy_function_generation(self):
        """测试能量函数生成"""
        response = self.interface.generate("Generate an energy function for tactic selection")
        
        self.assertTrue(response.success)
        self.assertIn("energy", response.content.lower())
    
    def test_llm_manager(self):
        """测试LLM管理器"""
        manager = create_llm_manager()
        
        self.assertIsNotNone(manager)
        self.assertIsNotNone(manager.interface)


class TestEnergyFunction(unittest.TestCase):
    """测试能量函数"""
    
    def setUp(self):
        self.spec = EnergyFunctionSpec(
            name="test_energy",
            description="测试能量函数",
            code="""
def energy_function(state, tactic, context):
    return 0.5 + len(state.goals) * 0.1
""",
            parameters={},
            complexity_score=1.0
        )
        self.energy_func = EnergyFunction(self.spec)
    
    def test_energy_function_creation(self):
        """测试能量函数创建"""
        self.assertTrue(self.energy_func.is_valid)
        self.assertIsNotNone(self.energy_func.function)
    
    def test_energy_evaluation(self):
        """测试能量评估"""
        # 创建测试数据
        state = ProofState(
            goals=["a + b = b + a", "x > 0"],
            hypotheses=["h1: a = b"],
            context={"variables": ["a", "b", "x"]}
        )
        
        tactic = TacticCandidate(
            tactic_name="rw",
            arguments=["h1"],
            confidence=0.8
        )
        
        context = TacticContext(
            goal_type="a + b = b + a",
            available_hypotheses=["h1"],
            available_variables=["a", "b"],
            proof_depth=1,
            previous_tactics=[]
        )
        
        energy = self.energy_func.evaluate(state, tactic, context)
        
        self.assertIsInstance(energy, float)
        self.assertGreater(energy, 0)
        # 应该是 0.5 + 2 * 0.1 = 0.7
        self.assertAlmostEqual(energy, 0.7, places=1)


class TestEnergyConstructor(unittest.TestCase):
    """测试能量函数构造器"""
    
    def setUp(self):
        self.llm_manager = create_llm_manager()
        self.constructor = EnergyFunctionConstructor(self.llm_manager)
    
    def test_constructor_creation(self):
        """测试构造器创建"""
        self.assertIsNotNone(self.constructor)
        self.assertIsNotNone(self.constructor.llm_manager)
    
    def test_energy_function_construction(self):
        """测试能量函数构造"""
        # 创建测试数据
        state = ProofState(
            goals=["a + b = b + a"],
            hypotheses=["h1: a = b"],
            context={"variables": ["a", "b"]}
        )
        
        tactics = [
            TacticCandidate("rw", ["h1"], 0.8),
            TacticCandidate("simp", [], 0.6)
        ]
        
        context = TacticContext(
            goal_type="a + b = b + a",
            available_hypotheses=["h1"],
            available_variables=["a", "b"],
            proof_depth=1,
            previous_tactics=[]
        )
        
        # 构造能量函数
        energy_func = self.constructor.construct_energy_function(state, tactics, context)
        
        self.assertIsNotNone(energy_func)
        self.assertIsInstance(energy_func, EnergyFunction)
        
        # 测试评估
        for tactic in tactics:
            energy = energy_func.evaluate(state, tactic, context)
            self.assertIsInstance(energy, float)


if __name__ == "__main__":
    # 运行测试
    unittest.main(verbosity=2)
