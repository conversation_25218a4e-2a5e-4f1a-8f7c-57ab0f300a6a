"""
Tactic类型定义和表示系统
"""

from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum
import numpy as np


class TacticCategory(Enum):
    """Tactic类别枚举"""
    REWRITE = "rewrite"          # 重写类：rw, simp
    APPLICATION = "application"   # 应用类：apply, exact
    INTRODUCTION = "introduction" # 引入类：intro, intros
    ELIMINATION = "elimination"   # 消除类：cases, induction
    COMPUTATION = "computation"   # 计算类：ring, norm_num, linarith
    SIMPLIFICATION = "simplification" # 简化类：simp, dsimp
    ASSUMPTION = "assumption"     # 假设类：assumption, exact
    STRUCTURAL = "structural"     # 结构类：constructor, left, right


@dataclass
class TacticSignature:
    """Tactic的签名信息"""
    name: str
    category: TacticCategory
    arity: int  # 参数数量
    parameter_types: List[str]  # 参数类型
    description: str
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "category": self.category.value,
            "arity": self.arity,
            "parameter_types": self.parameter_types,
            "description": self.description
        }


class TacticRegistry:
    """Tactic注册表，管理所有支持的tactic"""
    
    def __init__(self):
        self.tactics: Dict[str, TacticSignature] = {}
        self._initialize_default_tactics()
    
    def _initialize_default_tactics(self):
        """初始化默认的tactic集合"""
        default_tactics = [
            TacticSignature(
                name="rw",
                category=TacticCategory.REWRITE,
                arity=1,
                parameter_types=["expression"],
                description="Rewrite using an equality or iff"
            ),
            TacticSignature(
                name="simp",
                category=TacticCategory.SIMPLIFICATION,
                arity=0,
                parameter_types=[],
                description="Simplify the goal using simp lemmas"
            ),
            TacticSignature(
                name="apply",
                category=TacticCategory.APPLICATION,
                arity=1,
                parameter_types=["expression"],
                description="Apply a theorem or hypothesis"
            ),
            TacticSignature(
                name="exact",
                category=TacticCategory.ASSUMPTION,
                arity=1,
                parameter_types=["expression"],
                description="Provide an exact proof term"
            ),
            TacticSignature(
                name="intro",
                category=TacticCategory.INTRODUCTION,
                arity=0,
                parameter_types=[],
                description="Introduce a variable or hypothesis"
            ),
            TacticSignature(
                name="cases",
                category=TacticCategory.ELIMINATION,
                arity=1,
                parameter_types=["expression"],
                description="Case analysis on an inductive type"
            ),
            TacticSignature(
                name="induction",
                category=TacticCategory.ELIMINATION,
                arity=1,
                parameter_types=["expression"],
                description="Proof by induction"
            ),
            TacticSignature(
                name="ring",
                category=TacticCategory.COMPUTATION,
                arity=0,
                parameter_types=[],
                description="Solve ring equations"
            ),
            TacticSignature(
                name="linarith",
                category=TacticCategory.COMPUTATION,
                arity=0,
                parameter_types=[],
                description="Linear arithmetic solver"
            ),
            TacticSignature(
                name="norm_num",
                category=TacticCategory.COMPUTATION,
                arity=0,
                parameter_types=[],
                description="Normalize numerical expressions"
            )
        ]
        
        for tactic in default_tactics:
            self.tactics[tactic.name] = tactic
    
    def register_tactic(self, tactic: TacticSignature):
        """注册新的tactic"""
        self.tactics[tactic.name] = tactic
    
    def get_tactic(self, name: str) -> Optional[TacticSignature]:
        """获取tactic签名"""
        return self.tactics.get(name)
    
    def get_tactics_by_category(self, category: TacticCategory) -> List[TacticSignature]:
        """按类别获取tactics"""
        return [t for t in self.tactics.values() if t.category == category]
    
    def list_all_tactics(self) -> List[str]:
        """列出所有tactic名称"""
        return list(self.tactics.keys())


@dataclass
class TacticContext:
    """Tactic执行的上下文信息"""
    goal_type: str
    available_hypotheses: List[str]
    available_variables: List[str]
    proof_depth: int
    previous_tactics: List[str]
    
    def to_vector(self) -> np.ndarray:
        """将上下文转换为向量表示（用于机器学习）"""
        # 这是一个简化的向量化表示
        features = []
        
        # 目标类型特征（简化）
        goal_features = [
            1.0 if "=" in self.goal_type else 0.0,
            1.0 if "∀" in self.goal_type else 0.0,
            1.0 if "∃" in self.goal_type else 0.0,
            1.0 if "→" in self.goal_type else 0.0,
        ]
        features.extend(goal_features)
        
        # 上下文大小特征
        features.extend([
            len(self.available_hypotheses) / 10.0,  # 归一化
            len(self.available_variables) / 10.0,
            self.proof_depth / 20.0,
            len(self.previous_tactics) / 10.0
        ])
        
        return np.array(features, dtype=np.float32)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "goal_type": self.goal_type,
            "available_hypotheses": self.available_hypotheses,
            "available_variables": self.available_variables,
            "proof_depth": self.proof_depth,
            "previous_tactics": self.previous_tactics
        }


@dataclass
class TacticCandidate:
    """Tactic候选项，包含tactic及其在当前上下文中的相关信息"""
    tactic_name: str
    arguments: List[str]
    confidence: float  # 0-1之间的置信度
    estimated_energy: Optional[float] = None  # 预估的能量值
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "tactic_name": self.tactic_name,
            "arguments": self.arguments,
            "confidence": self.confidence,
            "estimated_energy": self.estimated_energy
        }


class TacticGenerator:
    """Tactic生成器，为给定上下文生成可能的tactic候选"""
    
    def __init__(self, registry: TacticRegistry):
        self.registry = registry
    
    def generate_candidates(self, context: TacticContext, max_candidates: int = 10) -> List[TacticCandidate]:
        """为给定上下文生成tactic候选"""
        candidates = []
        
        # 基于上下文特征选择合适的tactics
        for tactic_name, tactic_sig in self.registry.tactics.items():
            # 简单的启发式规则
            confidence = self._calculate_confidence(tactic_sig, context)
            
            if confidence > 0.1:  # 只考虑置信度较高的candidates
                # 生成参数（简化版本）
                args = self._generate_arguments(tactic_sig, context)
                
                candidate = TacticCandidate(
                    tactic_name=tactic_name,
                    arguments=args,
                    confidence=confidence
                )
                candidates.append(candidate)
        
        # 按置信度排序并返回前max_candidates个
        candidates.sort(key=lambda x: x.confidence, reverse=True)
        return candidates[:max_candidates]
    
    def _calculate_confidence(self, tactic: TacticSignature, context: TacticContext) -> float:
        """计算tactic在给定上下文中的置信度"""
        confidence = 0.5  # 基础置信度
        
        # 基于目标类型调整置信度
        if tactic.category == TacticCategory.REWRITE and "=" in context.goal_type:
            confidence += 0.3
        elif tactic.category == TacticCategory.COMPUTATION and any(op in context.goal_type for op in ["+", "*", "-"]):
            confidence += 0.3
        elif tactic.category == TacticCategory.INTRODUCTION and "∀" in context.goal_type:
            confidence += 0.3
        
        # 基于可用假设调整
        if tactic.name == "apply" and context.available_hypotheses:
            confidence += 0.2
        
        # 基于证明深度调整
        if context.proof_depth > 10 and tactic.category == TacticCategory.COMPUTATION:
            confidence += 0.1
        
        return min(1.0, confidence)
    
    def _generate_arguments(self, tactic: TacticSignature, context: TacticContext) -> List[str]:
        """为tactic生成参数"""
        if tactic.arity == 0:
            return []
        
        args = []
        for param_type in tactic.parameter_types:
            if param_type == "expression":
                # 从可用假设中选择
                if context.available_hypotheses:
                    args.append(context.available_hypotheses[0])
                else:
                    args.append("_")  # 占位符
        
        return args


# 全局tactic注册表实例
tactic_registry = TacticRegistry()
