#!/usr/bin/env python3
"""
基础演示 - 展示Lean Tactic能量函数项目的核心功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.data.lean_simulator import LeanDataSimulator, ProofState
from src.tactics.tactic_types import TacticGenerator, TacticContext, tactic_registry
from src.models.llm_interface import create_llm_manager
from src.energy.energy_constructor import EnergyFunctionConstructor
from src.energy.energy_evaluator import EnergyFunctionEvaluator
from src.utils.helpers import setup_logging, ProgressTracker, format_time
import time


def main():
    """主演示函数"""
    print("🚀 Lean Tactic能量函数项目演示")
    print("=" * 50)
    
    # 设置日志
    setup_logging("INFO")
    
    # 1. 生成模拟数据
    print("\n📊 1. 生成模拟Lean数据...")
    simulator = LeanDataSimulator()
    theorems = simulator.generate_dataset(5)  # 生成5个定理用于演示
    
    print(f"生成了 {len(theorems)} 个定理:")
    for i, theorem in enumerate(theorems):
        print(f"  {i+1}. {theorem.name} (难度: {theorem.difficulty}, 类别: {theorem.category})")
        print(f"     陈述: {theorem.statement}")
        print(f"     证明步骤: {len(theorem.proof_steps)} 步")
    
    # 2. 演示Tactic系统
    print("\n🎯 2. 演示Tactic表示系统...")
    tactic_generator = TacticGenerator(tactic_registry)
    
    # 选择第一个定理的第一步进行演示
    if theorems and theorems[0].proof_steps:
        first_step = theorems[0].proof_steps[0]
        context = TacticContext(
            goal_type=first_step.before_state.goals[0] if first_step.before_state.goals else "unknown",
            available_hypotheses=first_step.before_state.hypotheses,
            available_variables=first_step.before_state.context.get("variables", []),
            proof_depth=0,
            previous_tactics=[]
        )
        
        print(f"证明状态: {first_step.before_state}")
        print(f"上下文: {context.goal_type}")
        
        candidates = tactic_generator.generate_candidates(context, max_candidates=5)
        print(f"生成的Tactic候选 ({len(candidates)} 个):")
        for j, candidate in enumerate(candidates):
            print(f"  {j+1}. {candidate.tactic_name} (置信度: {candidate.confidence:.2f})")
    
    # 3. 演示大模型接口
    print("\n🤖 3. 演示大模型接口...")
    llm_manager = create_llm_manager()
    print(f"使用模型: {llm_manager.interface.model_name}")
    
    # 测试能量函数生成
    if theorems and theorems[0].proof_steps:
        state = theorems[0].proof_steps[0].before_state
        response = llm_manager.generate_energy_function(
            str(state), 
            ["rw", "simp", "apply"], 
            "简单的代数证明"
        )
        
        print(f"LLM响应成功: {response.success}")
        if response.success:
            print(f"生成的能量函数代码片段:")
            # 只显示前200个字符
            content_preview = response.content[:200] + "..." if len(response.content) > 200 else response.content
            print(f"  {content_preview}")
    
    # 4. 演示能量函数构造
    print("\n⚡ 4. 演示能量函数构造...")
    constructor = EnergyFunctionConstructor(llm_manager)
    
    if theorems and theorems[0].proof_steps:
        step = theorems[0].proof_steps[0]
        state = step.before_state
        context = TacticContext(
            goal_type=step.before_state.goals[0] if step.before_state.goals else "unknown",
            available_hypotheses=step.before_state.hypotheses,
            available_variables=step.before_state.context.get("variables", []),
            proof_depth=0,
            previous_tactics=[]
        )
        
        candidates = tactic_generator.generate_candidates(context, max_candidates=3)
        
        print(f"为证明状态构造能量函数...")
        start_time = time.time()
        
        try:
            energy_func = constructor.construct_energy_function(state, candidates, context)
            construction_time = time.time() - start_time
            
            print(f"✅ 能量函数构造成功 (耗时: {format_time(construction_time)})")
            print(f"函数名称: {energy_func.spec.name}")
            print(f"函数描述: {energy_func.spec.description}")
            print(f"函数有效性: {energy_func.is_valid}")
            
            # 评估每个候选tactic
            print(f"\nTactic能量评估:")
            for candidate in candidates:
                energy = energy_func.evaluate(state, candidate, context)
                print(f"  {candidate.tactic_name}: {energy:.3f}")
                
        except Exception as e:
            print(f"❌ 能量函数构造失败: {e}")
    
    # 5. 演示评估框架
    print("\n📈 5. 演示评估框架...")
    evaluator = EnergyFunctionEvaluator()
    
    if theorems:
        # 创建一个简单的测试能量函数
        from src.energy.energy_constructor import EnergyFunctionSpec, EnergyFunction
        
        test_spec = EnergyFunctionSpec(
            name="test_energy",
            description="测试能量函数",
            code="""
def energy_function(state, tactic, context):
    energy = 0.5
    if tactic.tactic_name == "simp":
        energy += 0.2
    elif tactic.tactic_name == "rw":
        energy += 0.1
    energy += len(state.goals) * 0.1
    return energy
""",
            parameters={},
            complexity_score=2.0
        )
        
        test_func = EnergyFunction(test_spec)
        
        print(f"评估测试能量函数...")
        start_time = time.time()
        
        try:
            result = evaluator.evaluate_function(test_func, theorems[:2])  # 只评估前2个定理
            eval_time = time.time() - start_time
            
            print(f"✅ 评估完成 (耗时: {format_time(eval_time)})")
            print(f"指标:")
            print(f"  准确率: {result.metrics.accuracy:.3f}")
            print(f"  平均能量: {result.metrics.mean_energy:.3f}")
            print(f"  成功率: {result.metrics.success_rate:.3f}")
            print(f"  平均执行时间: {format_time(result.metrics.execution_time)}")
            
        except Exception as e:
            print(f"❌ 评估失败: {e}")
    
    # 6. 总结
    print("\n🎉 演示完成!")
    print("=" * 50)
    print("本演示展示了以下核心功能:")
    print("✓ Lean数据模拟和生成")
    print("✓ Tactic表示和候选生成")
    print("✓ 大模型接口和能量函数生成")
    print("✓ 能量函数构造和评估")
    print("✓ 评估框架和指标计算")
    print("\n💡 这是一个基础实现，实际的Lean集成需要更复杂的工具链。")
    print("   但这个框架为进一步开发提供了良好的基础。")


if __name__ == "__main__":
    main()
