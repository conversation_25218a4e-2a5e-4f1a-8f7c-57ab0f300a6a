"""
大模型接口 - 与各种大语言模型交互的统一接口
"""

from typing import List, Dict, Any, Optional, Union
from abc import ABC, abstractmethod
import os
import json
import asyncio
from dataclasses import dataclass

# 可选依赖，根据需要导入
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    import anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False


@dataclass
class LLMResponse:
    """大模型响应的标准格式"""
    content: str
    usage: Dict[str, int]
    model: str
    success: bool
    error_message: Optional[str] = None


class BaseLLMInterface(ABC):
    """大模型接口的基类"""
    
    def __init__(self, model_name: str, api_key: Optional[str] = None):
        self.model_name = model_name
        self.api_key = api_key or self._get_api_key()
    
    @abstractmethod
    def _get_api_key(self) -> Optional[str]:
        """获取API密钥"""
        pass
    
    @abstractmethod
    def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """生成响应"""
        pass
    
    @abstractmethod
    async def generate_async(self, prompt: str, **kwargs) -> LLMResponse:
        """异步生成响应"""
        pass


class OpenAIInterface(BaseLLMInterface):
    """OpenAI模型接口"""
    
    def __init__(self, model_name: str = "gpt-4", api_key: Optional[str] = None):
        if not OPENAI_AVAILABLE:
            raise ImportError("OpenAI package not available. Install with: pip install openai")
        
        super().__init__(model_name, api_key)
        self.client = openai.OpenAI(api_key=self.api_key)
    
    def _get_api_key(self) -> Optional[str]:
        return os.getenv("OPENAI_API_KEY")
    
    def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """生成响应"""
        try:
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=kwargs.get("max_tokens", 2048),
                temperature=kwargs.get("temperature", 0.7),
                top_p=kwargs.get("top_p", 1.0)
            )
            
            return LLMResponse(
                content=response.choices[0].message.content,
                usage=response.usage.model_dump() if response.usage else {},
                model=response.model,
                success=True
            )
        
        except Exception as e:
            return LLMResponse(
                content="",
                usage={},
                model=self.model_name,
                success=False,
                error_message=str(e)
            )
    
    async def generate_async(self, prompt: str, **kwargs) -> LLMResponse:
        """异步生成响应"""
        # 简化版本，实际应该使用异步客户端
        return self.generate(prompt, **kwargs)


class MockLLMInterface(BaseLLMInterface):
    """模拟的LLM接口，用于测试和演示"""
    
    def __init__(self, model_name: str = "mock-gpt"):
        super().__init__(model_name, "mock-key")
    
    def _get_api_key(self) -> Optional[str]:
        return "mock-api-key"
    
    def generate(self, prompt: str, **kwargs) -> LLMResponse:
        """生成模拟响应"""
        # 基于prompt内容生成相应的模拟响应
        if "energy function" in prompt.lower():
            content = self._generate_mock_energy_function()
        elif "tactic" in prompt.lower():
            content = self._generate_mock_tactic_advice()
        else:
            content = "This is a mock response for testing purposes."
        
        return LLMResponse(
            content=content,
            usage={"prompt_tokens": len(prompt.split()), "completion_tokens": len(content.split())},
            model=self.model_name,
            success=True
        )
    
    async def generate_async(self, prompt: str, **kwargs) -> LLMResponse:
        """异步生成模拟响应"""
        await asyncio.sleep(0.1)  # 模拟网络延迟
        return self.generate(prompt, **kwargs)
    
    def _generate_mock_energy_function(self) -> str:
        """生成模拟的能量函数"""
        return """
Based on the proof state and available tactics, I suggest the following energy function:

```python
def energy_function(state, tactic, context):
    energy = 0.0

    # Base energy based on tactic type
    if tactic.name == "rw":
        energy += 0.2
    elif tactic.name == "simp":
        energy += 0.3
    elif tactic.name == "apply":
        energy += 0.4

    # Adjust based on goal complexity
    if len(state.goals) > 3:
        energy += 0.1

    # Prefer tactics that make progress
    if context.proof_depth > 5:
        energy += 0.1

    return energy
```

This energy function prioritizes rewrite tactics and penalizes complex states.
"""
    
    def _generate_mock_tactic_advice(self) -> str:
        """生成模拟的tactic建议"""
        return """
For the given proof state, I recommend the following tactics in order of preference:

1. **rw [hypothesis_name]** - If there's an equality hypothesis that can simplify the goal
2. **simp** - For general simplification when the goal contains complex expressions
3. **apply theorem_name** - If there's a relevant theorem that matches the goal structure
4. **intro** - If the goal is a universal quantification or implication

The energy values for these tactics would be approximately:
- rw: 0.2
- simp: 0.3  
- apply: 0.4
- intro: 0.5
"""


class LLMManager:
    """LLM管理器，统一管理不同的模型接口"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.interface = self._create_interface()
    
    def _create_interface(self) -> BaseLLMInterface:
        """根据配置创建相应的接口"""
        provider = self.config.get("provider", "mock").lower()
        model = self.config.get("model", "gpt-4")
        api_key = self.config.get("api_key")
        
        if provider == "openai":
            if not OPENAI_AVAILABLE:
                print("Warning: OpenAI not available, falling back to mock interface")
                return MockLLMInterface()
            return OpenAIInterface(model, api_key)
        
        elif provider == "anthropic":
            if not ANTHROPIC_AVAILABLE:
                print("Warning: Anthropic not available, falling back to mock interface")
                return MockLLMInterface()
            # TODO: 实现AnthropicInterface
            return MockLLMInterface()
        
        else:  # mock or unknown
            return MockLLMInterface(model)
    
    def generate_energy_function(self, proof_state: str, tactics: List[str], context: str) -> LLMResponse:
        """生成能量函数"""
        prompt = self._create_energy_function_prompt(proof_state, tactics, context)
        return self.interface.generate(
            prompt,
            max_tokens=self.config.get("max_tokens", 2048),
            temperature=self.config.get("temperature", 0.7)
        )
    
    def suggest_tactics(self, proof_state: str, context: str) -> LLMResponse:
        """建议tactics"""
        prompt = self._create_tactic_suggestion_prompt(proof_state, context)
        return self.interface.generate(
            prompt,
            max_tokens=self.config.get("max_tokens", 1024),
            temperature=self.config.get("temperature", 0.5)
        )
    
    def _create_energy_function_prompt(self, proof_state: str, tactics: List[str], context: str) -> str:
        """创建能量函数生成的prompt"""
        return f"""
You are an expert in formal mathematics and Lean theorem proving. Given a proof state and a list of available tactics, please construct an energy function that evaluates how good each tactic choice would be.

**Proof State:**
{proof_state}

**Available Tactics:**
{', '.join(tactics)}

**Context:**
{context}

Please provide a Python function that takes (state, tactic, context) as input and returns a float energy value. Lower energy values should indicate better tactic choices.

Consider factors like:
- Goal complexity reduction
- Hypothesis utilization
- Proof progress
- Tactic appropriateness for the goal type

Format your response as a complete Python function with explanatory comments.
"""
    
    def _create_tactic_suggestion_prompt(self, proof_state: str, context: str) -> str:
        """创建tactic建议的prompt"""
        return f"""
You are an expert Lean theorem prover. Given the following proof state, suggest the most appropriate tactics to apply next.

**Proof State:**
{proof_state}

**Context:**
{context}

Please provide:
1. A ranked list of 3-5 recommended tactics
2. Brief explanation for each recommendation
3. Estimated energy/priority values (lower = better)

Focus on tactics that are most likely to make meaningful progress toward completing the proof.
"""


# 便利函数
def create_llm_manager(config_path: Optional[str] = None) -> LLMManager:
    """创建LLM管理器的便利函数"""
    if config_path and os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config = json.load(f)
    else:
        # 默认配置
        config = {
            "provider": "mock",
            "model": "mock-gpt",
            "max_tokens": 2048,
            "temperature": 0.7
        }
    
    return LLMManager(config)
