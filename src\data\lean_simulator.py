"""
Lean数据模拟器 - 模拟从Lean mathlib中提取的数据结构
"""

from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import random
import json


class ProofState:
    """表示Lean证明状态"""
    
    def __init__(self, goals: List[str], hypotheses: List[str], context: Dict[str, Any]):
        self.goals = goals
        self.hypotheses = hypotheses
        self.context = context
        
    def __str__(self) -> str:
        return f"Goals: {self.goals}, Hypotheses: {self.hypotheses}"
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "goals": self.goals,
            "hypotheses": self.hypotheses,
            "context": self.context
        }


@dataclass
class TacticApplication:
    """表示一个tactic的应用"""
    tactic_name: str
    arguments: List[str]
    before_state: ProofState
    after_state: ProofState
    success: bool
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "tactic_name": self.tactic_name,
            "arguments": self.arguments,
            "before_state": self.before_state.to_dict(),
            "after_state": self.after_state.to_dict(),
            "success": self.success
        }


@dataclass
class Theorem:
    """表示一个数学定理"""
    name: str
    statement: str
    proof_steps: List[TacticApplication]
    difficulty: int  # 1-10的难度评级
    category: str    # 如"algebra", "topology", "number_theory"等
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "statement": self.statement,
            "proof_steps": [step.to_dict() for step in self.proof_steps],
            "difficulty": self.difficulty,
            "category": self.category
        }


class LeanDataSimulator:
    """模拟Lean mathlib数据的生成器"""
    
    def __init__(self):
        self.tactics = [
            "rw", "simp", "apply", "exact", "intro", 
            "cases", "induction", "ring", "linarith", "norm_num"
        ]
        
        self.categories = [
            "algebra", "topology", "number_theory", "analysis", 
            "geometry", "logic", "set_theory", "category_theory"
        ]
        
        # 模拟的定理模板
        self.theorem_templates = [
            {
                "name_pattern": "add_comm_{n}",
                "statement_pattern": "∀ (a b : ℕ), a + b = b + a",
                "category": "algebra",
                "difficulty": 2
            },
            {
                "name_pattern": "mul_assoc_{n}",
                "statement_pattern": "∀ (a b c : ℕ), (a * b) * c = a * (b * c)",
                "category": "algebra", 
                "difficulty": 3
            },
            {
                "name_pattern": "continuous_comp_{n}",
                "statement_pattern": "∀ f g, continuous f → continuous g → continuous (g ∘ f)",
                "category": "topology",
                "difficulty": 6
            }
        ]
    
    def generate_proof_state(self, complexity: int = 3) -> ProofState:
        """生成一个模拟的证明状态"""
        goals = []
        hypotheses = []
        
        # 生成目标
        for i in range(random.randint(1, complexity)):
            goal_types = [
                f"a_{i} + b_{i} = b_{i} + a_{i}",
                f"∀ x, P x → Q x",
                f"continuous f_{i}",
                f"x_{i} ∈ S_{i}"
            ]
            goals.append(random.choice(goal_types))
        
        # 生成假设
        for i in range(random.randint(0, complexity * 2)):
            hyp_types = [
                f"h_{i} : a_{i} = b_{i}",
                f"h_{i} : P a_{i}",
                f"h_{i} : x_{i} ∈ S_{i}",
                f"h_{i} : continuous f_{i}"
            ]
            hypotheses.append(random.choice(hyp_types))
        
        context = {
            "variables": [f"a_{i}" for i in range(complexity)],
            "types": ["ℕ", "ℝ", "Set α"],
            "complexity": complexity
        }
        
        return ProofState(goals, hypotheses, context)
    
    def generate_tactic_application(self, state: ProofState) -> TacticApplication:
        """为给定状态生成一个tactic应用"""
        tactic = random.choice(self.tactics)
        
        # 根据tactic类型生成参数
        if tactic == "rw":
            args = [f"h_{random.randint(0, 5)}", "←h_comm"]
        elif tactic == "apply":
            args = [f"theorem_{random.randint(0, 10)}"]
        elif tactic == "simp":
            args = ["*"] if random.random() > 0.5 else []
        else:
            args = []
        
        # 生成应用后的状态（简化版本）
        new_goals = state.goals.copy()
        if new_goals and random.random() > 0.3:  # 70%概率解决一个目标
            new_goals.pop(0)
        
        after_state = ProofState(
            new_goals, 
            state.hypotheses.copy(), 
            state.context.copy()
        )
        
        success = len(after_state.goals) < len(state.goals) or random.random() > 0.1
        
        return TacticApplication(
            tactic_name=tactic,
            arguments=args,
            before_state=state,
            after_state=after_state,
            success=success
        )
    
    def generate_theorem(self, theorem_id: int) -> Theorem:
        """生成一个模拟的定理"""
        template = random.choice(self.theorem_templates)
        
        name = template["name_pattern"].format(n=theorem_id)
        statement = template["statement_pattern"]
        category = template["category"]
        difficulty = template["difficulty"] + random.randint(-1, 1)
        difficulty = max(1, min(10, difficulty))
        
        # 生成证明步骤
        proof_steps = []
        current_state = self.generate_proof_state(difficulty)
        
        max_steps = random.randint(3, 15)
        for _ in range(max_steps):
            if not current_state.goals:  # 证明完成
                break
                
            tactic_app = self.generate_tactic_application(current_state)
            proof_steps.append(tactic_app)
            current_state = tactic_app.after_state
        
        return Theorem(
            name=name,
            statement=statement,
            proof_steps=proof_steps,
            difficulty=difficulty,
            category=category
        )
    
    def generate_dataset(self, num_theorems: int = 100) -> List[Theorem]:
        """生成一个定理数据集"""
        theorems = []
        for i in range(num_theorems):
            theorem = self.generate_theorem(i)
            theorems.append(theorem)
        return theorems
    
    def save_dataset(self, theorems: List[Theorem], filepath: str):
        """保存数据集到文件"""
        data = [theorem.to_dict() for theorem in theorems]
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def load_dataset(self, filepath: str) -> List[Theorem]:
        """从文件加载数据集"""
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        theorems = []
        for item in data:
            proof_steps = []
            for step_data in item["proof_steps"]:
                before_state = ProofState(**step_data["before_state"])
                after_state = ProofState(**step_data["after_state"])
                
                tactic_app = TacticApplication(
                    tactic_name=step_data["tactic_name"],
                    arguments=step_data["arguments"],
                    before_state=before_state,
                    after_state=after_state,
                    success=step_data["success"]
                )
                proof_steps.append(tactic_app)
            
            theorem = Theorem(
                name=item["name"],
                statement=item["statement"],
                proof_steps=proof_steps,
                difficulty=item["difficulty"],
                category=item["category"]
            )
            theorems.append(theorem)
        
        return theorems


if __name__ == "__main__":
    # 测试代码
    simulator = LeanDataSimulator()
    
    # 生成一个小数据集
    theorems = simulator.generate_dataset(10)
    
    print(f"生成了 {len(theorems)} 个定理")
    for theorem in theorems[:3]:
        print(f"\n定理: {theorem.name}")
        print(f"陈述: {theorem.statement}")
        print(f"难度: {theorem.difficulty}, 类别: {theorem.category}")
        print(f"证明步骤数: {len(theorem.proof_steps)}")
