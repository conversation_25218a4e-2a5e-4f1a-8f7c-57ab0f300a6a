[{"name": "add_comm_0", "statement": "∀ (a b : ℕ), a + b = b + a", "proof_steps": [{"tactic_name": "exact", "arguments": [], "before_state": {"goals": ["a_0 + b_0 = b_0 + a_0", "∀ x, P x → Q x"], "hypotheses": ["h_0 : P a_0"], "context": {"variables": ["a_0", "a_1"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 2}}, "after_state": {"goals": ["a_0 + b_0 = b_0 + a_0", "∀ x, P x → Q x"], "hypotheses": ["h_0 : P a_0"], "context": {"variables": ["a_0", "a_1"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 2}}, "success": true}, {"tactic_name": "ring", "arguments": [], "before_state": {"goals": ["a_0 + b_0 = b_0 + a_0", "∀ x, P x → Q x"], "hypotheses": ["h_0 : P a_0"], "context": {"variables": ["a_0", "a_1"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 2}}, "after_state": {"goals": ["∀ x, P x → Q x"], "hypotheses": ["h_0 : P a_0"], "context": {"variables": ["a_0", "a_1"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 2}}, "success": true}, {"tactic_name": "induction", "arguments": [], "before_state": {"goals": ["∀ x, P x → Q x"], "hypotheses": ["h_0 : P a_0"], "context": {"variables": ["a_0", "a_1"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 2}}, "after_state": {"goals": [], "hypotheses": ["h_0 : P a_0"], "context": {"variables": ["a_0", "a_1"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 2}}, "success": true}], "difficulty": 2, "category": "algebra"}, {"name": "mul_assoc_1", "statement": "∀ (a b c : ℕ), (a * b) * c = a * (b * c)", "proof_steps": [{"tactic_name": "exact", "arguments": [], "before_state": {"goals": ["∀ x, P x → Q x", "x_1 ∈ S_1", "a_2 + b_2 = b_2 + a_2", "continuous f_3"], "hypotheses": ["h_0 : x_0 ∈ S_0", "h_1 : P a_1", "h_2 : continuous f_2", "h_3 : continuous f_3", "h_4 : P a_4", "h_5 : continuous f_5", "h_6 : x_6 ∈ S_6"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "after_state": {"goals": ["x_1 ∈ S_1", "a_2 + b_2 = b_2 + a_2", "continuous f_3"], "hypotheses": ["h_0 : x_0 ∈ S_0", "h_1 : P a_1", "h_2 : continuous f_2", "h_3 : continuous f_3", "h_4 : P a_4", "h_5 : continuous f_5", "h_6 : x_6 ∈ S_6"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "success": true}, {"tactic_name": "rw", "arguments": ["h_1", "←h_comm"], "before_state": {"goals": ["x_1 ∈ S_1", "a_2 + b_2 = b_2 + a_2", "continuous f_3"], "hypotheses": ["h_0 : x_0 ∈ S_0", "h_1 : P a_1", "h_2 : continuous f_2", "h_3 : continuous f_3", "h_4 : P a_4", "h_5 : continuous f_5", "h_6 : x_6 ∈ S_6"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "after_state": {"goals": ["a_2 + b_2 = b_2 + a_2", "continuous f_3"], "hypotheses": ["h_0 : x_0 ∈ S_0", "h_1 : P a_1", "h_2 : continuous f_2", "h_3 : continuous f_3", "h_4 : P a_4", "h_5 : continuous f_5", "h_6 : x_6 ∈ S_6"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "success": true}, {"tactic_name": "induction", "arguments": [], "before_state": {"goals": ["a_2 + b_2 = b_2 + a_2", "continuous f_3"], "hypotheses": ["h_0 : x_0 ∈ S_0", "h_1 : P a_1", "h_2 : continuous f_2", "h_3 : continuous f_3", "h_4 : P a_4", "h_5 : continuous f_5", "h_6 : x_6 ∈ S_6"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "after_state": {"goals": ["a_2 + b_2 = b_2 + a_2", "continuous f_3"], "hypotheses": ["h_0 : x_0 ∈ S_0", "h_1 : P a_1", "h_2 : continuous f_2", "h_3 : continuous f_3", "h_4 : P a_4", "h_5 : continuous f_5", "h_6 : x_6 ∈ S_6"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "success": true}, {"tactic_name": "cases", "arguments": [], "before_state": {"goals": ["a_2 + b_2 = b_2 + a_2", "continuous f_3"], "hypotheses": ["h_0 : x_0 ∈ S_0", "h_1 : P a_1", "h_2 : continuous f_2", "h_3 : continuous f_3", "h_4 : P a_4", "h_5 : continuous f_5", "h_6 : x_6 ∈ S_6"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "after_state": {"goals": ["a_2 + b_2 = b_2 + a_2", "continuous f_3"], "hypotheses": ["h_0 : x_0 ∈ S_0", "h_1 : P a_1", "h_2 : continuous f_2", "h_3 : continuous f_3", "h_4 : P a_4", "h_5 : continuous f_5", "h_6 : x_6 ∈ S_6"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "success": true}, {"tactic_name": "norm_num", "arguments": [], "before_state": {"goals": ["a_2 + b_2 = b_2 + a_2", "continuous f_3"], "hypotheses": ["h_0 : x_0 ∈ S_0", "h_1 : P a_1", "h_2 : continuous f_2", "h_3 : continuous f_3", "h_4 : P a_4", "h_5 : continuous f_5", "h_6 : x_6 ∈ S_6"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "after_state": {"goals": ["a_2 + b_2 = b_2 + a_2", "continuous f_3"], "hypotheses": ["h_0 : x_0 ∈ S_0", "h_1 : P a_1", "h_2 : continuous f_2", "h_3 : continuous f_3", "h_4 : P a_4", "h_5 : continuous f_5", "h_6 : x_6 ∈ S_6"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "success": true}, {"tactic_name": "lina<PERSON>", "arguments": [], "before_state": {"goals": ["a_2 + b_2 = b_2 + a_2", "continuous f_3"], "hypotheses": ["h_0 : x_0 ∈ S_0", "h_1 : P a_1", "h_2 : continuous f_2", "h_3 : continuous f_3", "h_4 : P a_4", "h_5 : continuous f_5", "h_6 : x_6 ∈ S_6"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "after_state": {"goals": ["continuous f_3"], "hypotheses": ["h_0 : x_0 ∈ S_0", "h_1 : P a_1", "h_2 : continuous f_2", "h_3 : continuous f_3", "h_4 : P a_4", "h_5 : continuous f_5", "h_6 : x_6 ∈ S_6"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "success": true}, {"tactic_name": "cases", "arguments": [], "before_state": {"goals": ["continuous f_3"], "hypotheses": ["h_0 : x_0 ∈ S_0", "h_1 : P a_1", "h_2 : continuous f_2", "h_3 : continuous f_3", "h_4 : P a_4", "h_5 : continuous f_5", "h_6 : x_6 ∈ S_6"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "after_state": {"goals": ["continuous f_3"], "hypotheses": ["h_0 : x_0 ∈ S_0", "h_1 : P a_1", "h_2 : continuous f_2", "h_3 : continuous f_3", "h_4 : P a_4", "h_5 : continuous f_5", "h_6 : x_6 ∈ S_6"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "success": true}, {"tactic_name": "norm_num", "arguments": [], "before_state": {"goals": ["continuous f_3"], "hypotheses": ["h_0 : x_0 ∈ S_0", "h_1 : P a_1", "h_2 : continuous f_2", "h_3 : continuous f_3", "h_4 : P a_4", "h_5 : continuous f_5", "h_6 : x_6 ∈ S_6"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "after_state": {"goals": [], "hypotheses": ["h_0 : x_0 ∈ S_0", "h_1 : P a_1", "h_2 : continuous f_2", "h_3 : continuous f_3", "h_4 : P a_4", "h_5 : continuous f_5", "h_6 : x_6 ∈ S_6"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "success": true}], "difficulty": 4, "category": "algebra"}, {"name": "continuous_comp_2", "statement": "∀ f g, continuous f → continuous g → continuous (g ∘ f)", "proof_steps": [{"tactic_name": "exact", "arguments": [], "before_state": {"goals": ["a_0 + b_0 = b_0 + a_0", "a_1 + b_1 = b_1 + a_1", "a_2 + b_2 = b_2 + a_2", "continuous f_3", "continuous f_4"], "hypotheses": ["h_0 : P a_0", "h_1 : x_1 ∈ S_1", "h_2 : continuous f_2", "h_3 : x_3 ∈ S_3", "h_4 : P a_4", "h_5 : a_5 = b_5", "h_6 : a_6 = b_6", "h_7 : x_7 ∈ S_7", "h_8 : a_8 = b_8", "h_9 : P a_9", "h_10 : continuous f_10", "h_11 : P a_11"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4", "a_5", "a_6"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 7}}, "after_state": {"goals": ["a_1 + b_1 = b_1 + a_1", "a_2 + b_2 = b_2 + a_2", "continuous f_3", "continuous f_4"], "hypotheses": ["h_0 : P a_0", "h_1 : x_1 ∈ S_1", "h_2 : continuous f_2", "h_3 : x_3 ∈ S_3", "h_4 : P a_4", "h_5 : a_5 = b_5", "h_6 : a_6 = b_6", "h_7 : x_7 ∈ S_7", "h_8 : a_8 = b_8", "h_9 : P a_9", "h_10 : continuous f_10", "h_11 : P a_11"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4", "a_5", "a_6"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 7}}, "success": true}, {"tactic_name": "norm_num", "arguments": [], "before_state": {"goals": ["a_1 + b_1 = b_1 + a_1", "a_2 + b_2 = b_2 + a_2", "continuous f_3", "continuous f_4"], "hypotheses": ["h_0 : P a_0", "h_1 : x_1 ∈ S_1", "h_2 : continuous f_2", "h_3 : x_3 ∈ S_3", "h_4 : P a_4", "h_5 : a_5 = b_5", "h_6 : a_6 = b_6", "h_7 : x_7 ∈ S_7", "h_8 : a_8 = b_8", "h_9 : P a_9", "h_10 : continuous f_10", "h_11 : P a_11"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4", "a_5", "a_6"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 7}}, "after_state": {"goals": ["a_2 + b_2 = b_2 + a_2", "continuous f_3", "continuous f_4"], "hypotheses": ["h_0 : P a_0", "h_1 : x_1 ∈ S_1", "h_2 : continuous f_2", "h_3 : x_3 ∈ S_3", "h_4 : P a_4", "h_5 : a_5 = b_5", "h_6 : a_6 = b_6", "h_7 : x_7 ∈ S_7", "h_8 : a_8 = b_8", "h_9 : P a_9", "h_10 : continuous f_10", "h_11 : P a_11"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4", "a_5", "a_6"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 7}}, "success": true}, {"tactic_name": "intro", "arguments": [], "before_state": {"goals": ["a_2 + b_2 = b_2 + a_2", "continuous f_3", "continuous f_4"], "hypotheses": ["h_0 : P a_0", "h_1 : x_1 ∈ S_1", "h_2 : continuous f_2", "h_3 : x_3 ∈ S_3", "h_4 : P a_4", "h_5 : a_5 = b_5", "h_6 : a_6 = b_6", "h_7 : x_7 ∈ S_7", "h_8 : a_8 = b_8", "h_9 : P a_9", "h_10 : continuous f_10", "h_11 : P a_11"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4", "a_5", "a_6"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 7}}, "after_state": {"goals": ["continuous f_3", "continuous f_4"], "hypotheses": ["h_0 : P a_0", "h_1 : x_1 ∈ S_1", "h_2 : continuous f_2", "h_3 : x_3 ∈ S_3", "h_4 : P a_4", "h_5 : a_5 = b_5", "h_6 : a_6 = b_6", "h_7 : x_7 ∈ S_7", "h_8 : a_8 = b_8", "h_9 : P a_9", "h_10 : continuous f_10", "h_11 : P a_11"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4", "a_5", "a_6"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 7}}, "success": true}, {"tactic_name": "lina<PERSON>", "arguments": [], "before_state": {"goals": ["continuous f_3", "continuous f_4"], "hypotheses": ["h_0 : P a_0", "h_1 : x_1 ∈ S_1", "h_2 : continuous f_2", "h_3 : x_3 ∈ S_3", "h_4 : P a_4", "h_5 : a_5 = b_5", "h_6 : a_6 = b_6", "h_7 : x_7 ∈ S_7", "h_8 : a_8 = b_8", "h_9 : P a_9", "h_10 : continuous f_10", "h_11 : P a_11"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4", "a_5", "a_6"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 7}}, "after_state": {"goals": ["continuous f_4"], "hypotheses": ["h_0 : P a_0", "h_1 : x_1 ∈ S_1", "h_2 : continuous f_2", "h_3 : x_3 ∈ S_3", "h_4 : P a_4", "h_5 : a_5 = b_5", "h_6 : a_6 = b_6", "h_7 : x_7 ∈ S_7", "h_8 : a_8 = b_8", "h_9 : P a_9", "h_10 : continuous f_10", "h_11 : P a_11"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4", "a_5", "a_6"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 7}}, "success": true}], "difficulty": 7, "category": "topology"}, {"name": "continuous_comp_3", "statement": "∀ f g, continuous f → continuous g → continuous (g ∘ f)", "proof_steps": [{"tactic_name": "norm_num", "arguments": [], "before_state": {"goals": ["∀ x, P x → Q x", "∀ x, P x → Q x", "continuous f_2", "continuous f_3"], "hypotheses": ["h_0 : a_0 = b_0"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 5}}, "after_state": {"goals": ["∀ x, P x → Q x", "∀ x, P x → Q x", "continuous f_2", "continuous f_3"], "hypotheses": ["h_0 : a_0 = b_0"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 5}}, "success": true}, {"tactic_name": "norm_num", "arguments": [], "before_state": {"goals": ["∀ x, P x → Q x", "∀ x, P x → Q x", "continuous f_2", "continuous f_3"], "hypotheses": ["h_0 : a_0 = b_0"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 5}}, "after_state": {"goals": ["∀ x, P x → Q x", "continuous f_2", "continuous f_3"], "hypotheses": ["h_0 : a_0 = b_0"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 5}}, "success": true}, {"tactic_name": "apply", "arguments": ["theorem_2"], "before_state": {"goals": ["∀ x, P x → Q x", "continuous f_2", "continuous f_3"], "hypotheses": ["h_0 : a_0 = b_0"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 5}}, "after_state": {"goals": ["continuous f_2", "continuous f_3"], "hypotheses": ["h_0 : a_0 = b_0"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 5}}, "success": true}, {"tactic_name": "ring", "arguments": [], "before_state": {"goals": ["continuous f_2", "continuous f_3"], "hypotheses": ["h_0 : a_0 = b_0"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 5}}, "after_state": {"goals": ["continuous f_2", "continuous f_3"], "hypotheses": ["h_0 : a_0 = b_0"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 5}}, "success": true}, {"tactic_name": "induction", "arguments": [], "before_state": {"goals": ["continuous f_2", "continuous f_3"], "hypotheses": ["h_0 : a_0 = b_0"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 5}}, "after_state": {"goals": ["continuous f_3"], "hypotheses": ["h_0 : a_0 = b_0"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 5}}, "success": true}, {"tactic_name": "simp", "arguments": ["*"], "before_state": {"goals": ["continuous f_3"], "hypotheses": ["h_0 : a_0 = b_0"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 5}}, "after_state": {"goals": [], "hypotheses": ["h_0 : a_0 = b_0"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 5}}, "success": true}], "difficulty": 5, "category": "topology"}, {"name": "mul_assoc_4", "statement": "∀ (a b c : ℕ), (a * b) * c = a * (b * c)", "proof_steps": [{"tactic_name": "apply", "arguments": ["theorem_4"], "before_state": {"goals": ["x_0 ∈ S_0", "a_1 + b_1 = b_1 + a_1", "a_2 + b_2 = b_2 + a_2"], "hypotheses": ["h_0 : continuous f_0", "h_1 : continuous f_1", "h_2 : continuous f_2"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "after_state": {"goals": ["a_1 + b_1 = b_1 + a_1", "a_2 + b_2 = b_2 + a_2"], "hypotheses": ["h_0 : continuous f_0", "h_1 : continuous f_1", "h_2 : continuous f_2"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "success": true}, {"tactic_name": "lina<PERSON>", "arguments": [], "before_state": {"goals": ["a_1 + b_1 = b_1 + a_1", "a_2 + b_2 = b_2 + a_2"], "hypotheses": ["h_0 : continuous f_0", "h_1 : continuous f_1", "h_2 : continuous f_2"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "after_state": {"goals": ["a_2 + b_2 = b_2 + a_2"], "hypotheses": ["h_0 : continuous f_0", "h_1 : continuous f_1", "h_2 : continuous f_2"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "success": true}, {"tactic_name": "rw", "arguments": ["h_5", "←h_comm"], "before_state": {"goals": ["a_2 + b_2 = b_2 + a_2"], "hypotheses": ["h_0 : continuous f_0", "h_1 : continuous f_1", "h_2 : continuous f_2"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "after_state": {"goals": ["a_2 + b_2 = b_2 + a_2"], "hypotheses": ["h_0 : continuous f_0", "h_1 : continuous f_1", "h_2 : continuous f_2"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "success": true}, {"tactic_name": "cases", "arguments": [], "before_state": {"goals": ["a_2 + b_2 = b_2 + a_2"], "hypotheses": ["h_0 : continuous f_0", "h_1 : continuous f_1", "h_2 : continuous f_2"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "after_state": {"goals": [], "hypotheses": ["h_0 : continuous f_0", "h_1 : continuous f_1", "h_2 : continuous f_2"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 4}}, "success": true}], "difficulty": 4, "category": "algebra"}, {"name": "continuous_comp_5", "statement": "∀ f g, continuous f → continuous g → continuous (g ∘ f)", "proof_steps": [{"tactic_name": "lina<PERSON>", "arguments": [], "before_state": {"goals": ["a_0 + b_0 = b_0 + a_0", "continuous f_1", "continuous f_2", "x_3 ∈ S_3", "a_4 + b_4 = b_4 + a_4", "x_5 ∈ S_5"], "hypotheses": ["h_0 : continuous f_0", "h_1 : continuous f_1", "h_2 : continuous f_2", "h_3 : continuous f_3", "h_4 : a_4 = b_4", "h_5 : P a_5", "h_6 : a_6 = b_6", "h_7 : x_7 ∈ S_7", "h_8 : x_8 ∈ S_8"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4", "a_5"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 6}}, "after_state": {"goals": ["continuous f_1", "continuous f_2", "x_3 ∈ S_3", "a_4 + b_4 = b_4 + a_4", "x_5 ∈ S_5"], "hypotheses": ["h_0 : continuous f_0", "h_1 : continuous f_1", "h_2 : continuous f_2", "h_3 : continuous f_3", "h_4 : a_4 = b_4", "h_5 : P a_5", "h_6 : a_6 = b_6", "h_7 : x_7 ∈ S_7", "h_8 : x_8 ∈ S_8"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4", "a_5"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 6}}, "success": true}, {"tactic_name": "apply", "arguments": ["theorem_3"], "before_state": {"goals": ["continuous f_1", "continuous f_2", "x_3 ∈ S_3", "a_4 + b_4 = b_4 + a_4", "x_5 ∈ S_5"], "hypotheses": ["h_0 : continuous f_0", "h_1 : continuous f_1", "h_2 : continuous f_2", "h_3 : continuous f_3", "h_4 : a_4 = b_4", "h_5 : P a_5", "h_6 : a_6 = b_6", "h_7 : x_7 ∈ S_7", "h_8 : x_8 ∈ S_8"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4", "a_5"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 6}}, "after_state": {"goals": ["continuous f_2", "x_3 ∈ S_3", "a_4 + b_4 = b_4 + a_4", "x_5 ∈ S_5"], "hypotheses": ["h_0 : continuous f_0", "h_1 : continuous f_1", "h_2 : continuous f_2", "h_3 : continuous f_3", "h_4 : a_4 = b_4", "h_5 : P a_5", "h_6 : a_6 = b_6", "h_7 : x_7 ∈ S_7", "h_8 : x_8 ∈ S_8"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4", "a_5"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 6}}, "success": true}, {"tactic_name": "apply", "arguments": ["theorem_1"], "before_state": {"goals": ["continuous f_2", "x_3 ∈ S_3", "a_4 + b_4 = b_4 + a_4", "x_5 ∈ S_5"], "hypotheses": ["h_0 : continuous f_0", "h_1 : continuous f_1", "h_2 : continuous f_2", "h_3 : continuous f_3", "h_4 : a_4 = b_4", "h_5 : P a_5", "h_6 : a_6 = b_6", "h_7 : x_7 ∈ S_7", "h_8 : x_8 ∈ S_8"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4", "a_5"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 6}}, "after_state": {"goals": ["continuous f_2", "x_3 ∈ S_3", "a_4 + b_4 = b_4 + a_4", "x_5 ∈ S_5"], "hypotheses": ["h_0 : continuous f_0", "h_1 : continuous f_1", "h_2 : continuous f_2", "h_3 : continuous f_3", "h_4 : a_4 = b_4", "h_5 : P a_5", "h_6 : a_6 = b_6", "h_7 : x_7 ∈ S_7", "h_8 : x_8 ∈ S_8"], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4", "a_5"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 6}}, "success": true}], "difficulty": 6, "category": "topology"}, {"name": "mul_assoc_6", "statement": "∀ (a b c : ℕ), (a * b) * c = a * (b * c)", "proof_steps": [{"tactic_name": "rw", "arguments": ["h_3", "←h_comm"], "before_state": {"goals": ["x_0 ∈ S_0", "∀ x, P x → Q x"], "hypotheses": ["h_0 : x_0 ∈ S_0", "h_1 : continuous f_1"], "context": {"variables": ["a_0", "a_1"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 2}}, "after_state": {"goals": ["∀ x, P x → Q x"], "hypotheses": ["h_0 : x_0 ∈ S_0", "h_1 : continuous f_1"], "context": {"variables": ["a_0", "a_1"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 2}}, "success": true}, {"tactic_name": "induction", "arguments": [], "before_state": {"goals": ["∀ x, P x → Q x"], "hypotheses": ["h_0 : x_0 ∈ S_0", "h_1 : continuous f_1"], "context": {"variables": ["a_0", "a_1"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 2}}, "after_state": {"goals": [], "hypotheses": ["h_0 : x_0 ∈ S_0", "h_1 : continuous f_1"], "context": {"variables": ["a_0", "a_1"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 2}}, "success": true}], "difficulty": 2, "category": "algebra"}, {"name": "add_comm_7", "statement": "∀ (a b : ℕ), a + b = b + a", "proof_steps": [{"tactic_name": "intro", "arguments": [], "before_state": {"goals": ["x_0 ∈ S_0", "x_1 ∈ S_1"], "hypotheses": ["h_0 : a_0 = b_0"], "context": {"variables": ["a_0", "a_1", "a_2"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 3}}, "after_state": {"goals": ["x_0 ∈ S_0", "x_1 ∈ S_1"], "hypotheses": ["h_0 : a_0 = b_0"], "context": {"variables": ["a_0", "a_1", "a_2"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 3}}, "success": true}, {"tactic_name": "apply", "arguments": ["theorem_4"], "before_state": {"goals": ["x_0 ∈ S_0", "x_1 ∈ S_1"], "hypotheses": ["h_0 : a_0 = b_0"], "context": {"variables": ["a_0", "a_1", "a_2"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 3}}, "after_state": {"goals": ["x_0 ∈ S_0", "x_1 ∈ S_1"], "hypotheses": ["h_0 : a_0 = b_0"], "context": {"variables": ["a_0", "a_1", "a_2"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 3}}, "success": true}, {"tactic_name": "exact", "arguments": [], "before_state": {"goals": ["x_0 ∈ S_0", "x_1 ∈ S_1"], "hypotheses": ["h_0 : a_0 = b_0"], "context": {"variables": ["a_0", "a_1", "a_2"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 3}}, "after_state": {"goals": ["x_1 ∈ S_1"], "hypotheses": ["h_0 : a_0 = b_0"], "context": {"variables": ["a_0", "a_1", "a_2"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 3}}, "success": true}, {"tactic_name": "exact", "arguments": [], "before_state": {"goals": ["x_1 ∈ S_1"], "hypotheses": ["h_0 : a_0 = b_0"], "context": {"variables": ["a_0", "a_1", "a_2"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 3}}, "after_state": {"goals": [], "hypotheses": ["h_0 : a_0 = b_0"], "context": {"variables": ["a_0", "a_1", "a_2"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 3}}, "success": true}], "difficulty": 3, "category": "algebra"}, {"name": "mul_assoc_8", "statement": "∀ (a b c : ℕ), (a * b) * c = a * (b * c)", "proof_steps": [{"tactic_name": "norm_num", "arguments": [], "before_state": {"goals": ["continuous f_0", "a_1 + b_1 = b_1 + a_1"], "hypotheses": [], "context": {"variables": ["a_0", "a_1"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 2}}, "after_state": {"goals": ["a_1 + b_1 = b_1 + a_1"], "hypotheses": [], "context": {"variables": ["a_0", "a_1"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 2}}, "success": true}, {"tactic_name": "ring", "arguments": [], "before_state": {"goals": ["a_1 + b_1 = b_1 + a_1"], "hypotheses": [], "context": {"variables": ["a_0", "a_1"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 2}}, "after_state": {"goals": ["a_1 + b_1 = b_1 + a_1"], "hypotheses": [], "context": {"variables": ["a_0", "a_1"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 2}}, "success": true}, {"tactic_name": "ring", "arguments": [], "before_state": {"goals": ["a_1 + b_1 = b_1 + a_1"], "hypotheses": [], "context": {"variables": ["a_0", "a_1"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 2}}, "after_state": {"goals": [], "hypotheses": [], "context": {"variables": ["a_0", "a_1"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 2}}, "success": true}], "difficulty": 2, "category": "algebra"}, {"name": "continuous_comp_9", "statement": "∀ f g, continuous f → continuous g → continuous (g ∘ f)", "proof_steps": [{"tactic_name": "simp", "arguments": ["*"], "before_state": {"goals": ["x_0 ∈ S_0", "x_1 ∈ S_1"], "hypotheses": [], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 5}}, "after_state": {"goals": ["x_1 ∈ S_1"], "hypotheses": [], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 5}}, "success": true}, {"tactic_name": "ring", "arguments": [], "before_state": {"goals": ["x_1 ∈ S_1"], "hypotheses": [], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 5}}, "after_state": {"goals": [], "hypotheses": [], "context": {"variables": ["a_0", "a_1", "a_2", "a_3", "a_4"], "types": ["ℕ", "ℝ", "Set α"], "complexity": 5}}, "success": true}], "difficulty": 5, "category": "topology"}]