"""
能量函数评估器 - 评估能量函数的质量和性能
"""

from typing import List, Dict, Any, Tu<PERSON>, Optional
import numpy as np
from dataclasses import dataclass
import time
import statistics

from .energy_constructor import EnergyFunction, EnergyFunctionConstructor
from ..data.lean_simulator import Theorem, TacticApplication, ProofState
from ..tactics.tactic_types import TacticCandidate, TacticContext


@dataclass
class EvaluationMetrics:
    """评估指标"""
    accuracy: float  # 预测准确率
    mean_energy: float  # 平均能量值
    energy_variance: float  # 能量值方差
    ranking_correlation: float  # 排序相关性
    execution_time: float  # 执行时间（秒）
    success_rate: float  # 成功率（函数正常执行的比例）
    
    def to_dict(self) -> Dict[str, float]:
        return {
            "accuracy": self.accuracy,
            "mean_energy": self.mean_energy,
            "energy_variance": self.energy_variance,
            "ranking_correlation": self.ranking_correlation,
            "execution_time": self.execution_time,
            "success_rate": self.success_rate
        }


@dataclass
class EvaluationResult:
    """评估结果"""
    function_name: str
    metrics: EvaluationMetrics
    detailed_results: List[Dict[str, Any]]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "function_name": self.function_name,
            "metrics": self.metrics.to_dict(),
            "detailed_results": self.detailed_results
        }


class EnergyFunctionEvaluator:
    """能量函数评估器"""
    
    def __init__(self):
        self.evaluation_cache: Dict[str, EvaluationResult] = {}
    
    def evaluate_function(
        self, 
        energy_func: EnergyFunction, 
        test_data: List[Theorem],
        ground_truth: Optional[Dict[str, float]] = None
    ) -> EvaluationResult:
        """评估单个能量函数"""
        
        detailed_results = []
        execution_times = []
        success_count = 0
        energy_values = []
        predictions = []
        actual_values = []
        
        for theorem in test_data:
            for i, step in enumerate(theorem.proof_steps):
                # 准备评估数据
                state = step.before_state
                context = self._create_context_from_step(step, theorem, i)
                
                # 创建tactic候选
                tactic = TacticCandidate(
                    tactic_name=step.tactic_name,
                    arguments=step.arguments,
                    confidence=1.0 if step.success else 0.0
                )
                
                # 评估能量函数
                start_time = time.time()
                try:
                    energy = energy_func.evaluate(state, tactic, context)
                    execution_time = time.time() - start_time
                    success = True
                    success_count += 1
                except Exception as e:
                    energy = float('inf')
                    execution_time = time.time() - start_time
                    success = False
                
                execution_times.append(execution_time)
                energy_values.append(energy if energy != float('inf') else 10.0)
                
                # 记录详细结果
                result = {
                    "theorem_name": theorem.name,
                    "step_index": i,
                    "tactic_name": step.tactic_name,
                    "energy": energy,
                    "success": success,
                    "execution_time": execution_time,
                    "ground_truth_success": step.success
                }
                detailed_results.append(result)
                
                # 收集预测和实际值用于相关性计算
                if success and energy != float('inf'):
                    predictions.append(energy)
                    # 使用成功率作为"真实"能量的代理
                    actual_values.append(0.0 if step.success else 1.0)
        
        # 计算指标
        metrics = self._calculate_metrics(
            energy_values, execution_times, success_count, 
            len(detailed_results), predictions, actual_values
        )
        
        return EvaluationResult(
            function_name=energy_func.spec.name,
            metrics=metrics,
            detailed_results=detailed_results
        )
    
    def compare_functions(
        self, 
        functions: List[EnergyFunction], 
        test_data: List[Theorem]
    ) -> Dict[str, EvaluationResult]:
        """比较多个能量函数"""
        results = {}
        
        for func in functions:
            result = self.evaluate_function(func, test_data)
            results[func.spec.name] = result
        
        return results
    
    def benchmark_constructor(
        self, 
        constructor: EnergyFunctionConstructor, 
        test_data: List[Theorem],
        num_samples: int = 10
    ) -> Dict[str, Any]:
        """基准测试能量函数构造器"""
        
        construction_times = []
        evaluation_results = []
        
        # 随机选择测试样本
        import random
        sample_theorems = random.sample(test_data, min(num_samples, len(test_data)))
        
        for theorem in sample_theorems:
            for i, step in enumerate(theorem.proof_steps[:3]):  # 只测试前3步
                state = step.before_state
                context = self._create_context_from_step(step, theorem, i)
                
                # 创建候选tactics
                candidates = [
                    TacticCandidate(step.tactic_name, step.arguments, 1.0),
                    TacticCandidate("simp", [], 0.8),
                    TacticCandidate("rw", ["h"], 0.6)
                ]
                
                # 测试构造时间
                start_time = time.time()
                try:
                    energy_func = constructor.construct_energy_function(
                        state, candidates, context
                    )
                    construction_time = time.time() - start_time
                    construction_success = True
                except Exception as e:
                    construction_time = time.time() - start_time
                    construction_success = False
                    energy_func = None
                
                construction_times.append(construction_time)
                
                # 如果构造成功，评估函数质量
                if construction_success and energy_func:
                    eval_result = self.evaluate_function(energy_func, [theorem])
                    evaluation_results.append(eval_result.metrics)
        
        # 汇总结果
        avg_construction_time = statistics.mean(construction_times) if construction_times else 0
        success_rate = sum(1 for t in construction_times if t < 10.0) / len(construction_times) if construction_times else 0
        
        avg_metrics = {}
        if evaluation_results:
            metric_names = evaluation_results[0].to_dict().keys()
            for metric in metric_names:
                values = [r.to_dict()[metric] for r in evaluation_results if not np.isnan(r.to_dict()[metric])]
                avg_metrics[f"avg_{metric}"] = statistics.mean(values) if values else 0
        
        return {
            "avg_construction_time": avg_construction_time,
            "construction_success_rate": success_rate,
            "num_samples": len(construction_times),
            **avg_metrics
        }
    
    def _create_context_from_step(self, step: TacticApplication, theorem: Theorem, step_index: int) -> TacticContext:
        """从证明步骤创建上下文"""
        previous_tactics = [s.tactic_name for s in theorem.proof_steps[:step_index]]
        
        return TacticContext(
            goal_type=step.before_state.goals[0] if step.before_state.goals else "unknown",
            available_hypotheses=step.before_state.hypotheses,
            available_variables=step.before_state.context.get("variables", []),
            proof_depth=step_index,
            previous_tactics=previous_tactics
        )
    
    def _calculate_metrics(
        self, 
        energy_values: List[float], 
        execution_times: List[float],
        success_count: int, 
        total_count: int,
        predictions: List[float],
        actual_values: List[float]
    ) -> EvaluationMetrics:
        """计算评估指标"""
        
        # 基本统计
        mean_energy = statistics.mean(energy_values) if energy_values else 0
        energy_variance = statistics.variance(energy_values) if len(energy_values) > 1 else 0
        avg_execution_time = statistics.mean(execution_times) if execution_times else 0
        success_rate = success_count / total_count if total_count > 0 else 0
        
        # 计算准确率（简化版本）
        # 这里假设低能量对应好的tactic选择
        accuracy = 0.0
        if predictions and actual_values and len(predictions) == len(actual_values):
            # 计算预测和实际值的相关性
            try:
                correlation = np.corrcoef(predictions, actual_values)[0, 1]
                accuracy = max(0, -correlation)  # 负相关表示低能量对应好结果
            except:
                accuracy = 0.0
        
        # 排序相关性（简化版本）
        ranking_correlation = accuracy  # 简化处理
        
        return EvaluationMetrics(
            accuracy=accuracy,
            mean_energy=mean_energy,
            energy_variance=energy_variance,
            ranking_correlation=ranking_correlation,
            execution_time=avg_execution_time,
            success_rate=success_rate
        )
    
    def generate_report(self, results: Dict[str, EvaluationResult]) -> str:
        """生成评估报告"""
        report = "# 能量函数评估报告\n\n"
        
        # 汇总表格
        report += "## 指标汇总\n\n"
        report += "| 函数名称 | 准确率 | 平均能量 | 成功率 | 执行时间(ms) |\n"
        report += "|---------|--------|----------|--------|-------------|\n"
        
        for name, result in results.items():
            metrics = result.metrics
            report += f"| {name} | {metrics.accuracy:.3f} | {metrics.mean_energy:.3f} | {metrics.success_rate:.3f} | {metrics.execution_time*1000:.2f} |\n"
        
        # 详细分析
        report += "\n## 详细分析\n\n"
        
        for name, result in results.items():
            report += f"### {name}\n\n"
            metrics = result.metrics
            
            report += f"- **准确率**: {metrics.accuracy:.3f}\n"
            report += f"- **平均能量**: {metrics.mean_energy:.3f}\n"
            report += f"- **能量方差**: {metrics.energy_variance:.3f}\n"
            report += f"- **排序相关性**: {metrics.ranking_correlation:.3f}\n"
            report += f"- **执行时间**: {metrics.execution_time*1000:.2f}ms\n"
            report += f"- **成功率**: {metrics.success_rate:.3f}\n\n"
        
        return report
