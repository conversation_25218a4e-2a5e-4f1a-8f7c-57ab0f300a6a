"""
工具函数和辅助类
"""

import yaml
import json
import os
from typing import Dict, Any, List, Optional
import logging
from pathlib import Path


def load_config(config_path: str = "config.yaml") -> Dict[str, Any]:
    """加载配置文件"""
    if not os.path.exists(config_path):
        # 返回默认配置
        return {
            "llm": {
                "provider": "mock",
                "model": "mock-gpt",
                "max_tokens": 2048,
                "temperature": 0.7
            },
            "data": {
                "max_theorems": 100,
                "max_proof_steps": 50
            },
            "evaluation": {
                "test_split": 0.2,
                "validation_split": 0.1
            }
        }
    
    with open(config_path, 'r', encoding='utf-8') as f:
        if config_path.endswith('.yaml') or config_path.endswith('.yml'):
            return yaml.safe_load(f)
        else:
            return json.load(f)


def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None):
    """设置日志"""
    level = getattr(logging, log_level.upper(), logging.INFO)
    
    handlers = [logging.StreamHandler()]
    if log_file:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        handlers.append(logging.FileHandler(log_file))
    
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=handlers
    )


def ensure_directory(path: str):
    """确保目录存在"""
    Path(path).mkdir(parents=True, exist_ok=True)


def save_results(results: Dict[str, Any], filepath: str):
    """保存结果到文件"""
    ensure_directory(os.path.dirname(filepath))
    
    with open(filepath, 'w', encoding='utf-8') as f:
        if filepath.endswith('.yaml') or filepath.endswith('.yml'):
            yaml.dump(results, f, default_flow_style=False, allow_unicode=True)
        else:
            json.dump(results, f, indent=2, ensure_ascii=False)


def load_results(filepath: str) -> Dict[str, Any]:
    """从文件加载结果"""
    with open(filepath, 'r', encoding='utf-8') as f:
        if filepath.endswith('.yaml') or filepath.endswith('.yml'):
            return yaml.safe_load(f)
        else:
            return json.load(f)


class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, total: int, description: str = "Processing"):
        self.total = total
        self.current = 0
        self.description = description
    
    def update(self, increment: int = 1):
        """更新进度"""
        self.current += increment
        percentage = (self.current / self.total) * 100
        print(f"\r{self.description}: {self.current}/{self.total} ({percentage:.1f}%)", end="")
        
        if self.current >= self.total:
            print()  # 换行
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.current < self.total:
            print()  # 确保换行


def format_time(seconds: float) -> str:
    """格式化时间"""
    if seconds < 1:
        return f"{seconds*1000:.1f}ms"
    elif seconds < 60:
        return f"{seconds:.1f}s"
    else:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}m{secs:.1f}s"


def truncate_string(s: str, max_length: int = 100) -> str:
    """截断字符串"""
    if len(s) <= max_length:
        return s
    return s[:max_length-3] + "..."


def validate_energy_function_code(code: str) -> bool:
    """验证能量函数代码的基本语法"""
    try:
        import ast
        ast.parse(code)
        
        # 检查是否包含函数定义
        tree = ast.parse(code)
        has_function = any(isinstance(node, ast.FunctionDef) for node in ast.walk(tree))
        
        return has_function
    except SyntaxError:
        return False


def extract_function_name(code: str) -> Optional[str]:
    """从代码中提取函数名"""
    try:
        import ast
        tree = ast.parse(code)
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                return node.name
        
        return None
    except:
        return None


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = config_path
        self.config = load_config(config_path)
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值，支持点号分隔的嵌套键"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save(self):
        """保存配置到文件"""
        save_results(self.config, self.config_path)


def create_sample_data_if_not_exists():
    """如果不存在样本数据则创建"""
    data_dir = "data"
    sample_file = os.path.join(data_dir, "sample_theorems.json")
    
    if not os.path.exists(sample_file):
        ensure_directory(data_dir)
        
        # 创建样本数据
        from ..data.lean_simulator import LeanDataSimulator
        
        simulator = LeanDataSimulator()
        theorems = simulator.generate_dataset(20)
        simulator.save_dataset(theorems, sample_file)
        
        print(f"Created sample data: {sample_file}")
    
    return sample_file
