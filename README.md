# Lean Tactic Energy Function Project

这个项目旨在使用Lean的mathlib库作为数据集，通过大模型为tactic选择构造能量函数。

## 项目概述

本项目实现了一个基础框架，用于：
1. 模拟从Lean mathlib中提取数学定理和证明数据
2. 表示和处理Lean tactics
3. 使用大模型生成tactic选择的能量函数
4. 评估能量函数的质量

## 项目结构

```
├── src/
│   ├── data/              # 数据处理模块
│   │   ├── lean_simulator.py    # Lean数据模拟器
│   │   └── mathlib_extractor.py # Mathlib数据提取器
│   ├── tactics/           # Tactic表示和处理
│   │   ├── tactic_types.py      # Tactic类型定义
│   │   └── tactic_context.py    # Tactic上下文管理
│   ├── models/            # 大模型接口
│   │   ├── llm_interface.py     # 大模型接口
│   │   └── prompt_templates.py  # 提示模板
│   ├── energy/            # 能量函数相关
│   │   ├── energy_constructor.py # 能量函数构造器
│   │   └── energy_evaluator.py  # 能量函数评估器
│   └── utils/             # 工具函数
│       └── helpers.py
├── tests/                 # 测试文件
├── examples/              # 示例和演示
├── requirements.txt       # Python依赖
└── config.yaml           # 配置文件
```

## 安装和使用

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 运行示例：
```bash
python examples/basic_demo.py
```

## 核心概念

### Tactic能量函数
能量函数用于评估在给定证明状态下选择特定tactic的"好坏"程度。较低的能量值表示更好的tactic选择。

### 数据流程
1. 从模拟的mathlib数据中提取定理和证明状态
2. 为每个证明状态生成可能的tactic选择
3. 使用大模型生成能量函数
4. 评估能量函数的质量

## 注意事项

这是一个基础实现，用于概念验证。实际的Lean集成需要更复杂的工具链。
