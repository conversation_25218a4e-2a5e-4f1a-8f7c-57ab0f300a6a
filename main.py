#!/usr/bin/env python3
"""
Lean Tactic能量函数项目 - 主入口文件
"""

import argparse
import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent / "src"))

from src.utils.helpers import setup_logging, load_config, ConfigManager
from examples.basic_demo import main as demo_main


def run_demo():
    """运行基础演示"""
    print("🚀 运行基础演示...")
    demo_main()


def run_tests():
    """运行测试"""
    print("🧪 运行测试...")
    import subprocess
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pytest", "tests/", "-v"
        ], capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        return result.returncode == 0
    except FileNotFoundError:
        print("❌ pytest未安装，尝试使用unittest...")
        try:
            result = subprocess.run([
                sys.executable, "-m", "unittest", "discover", "tests", "-v"
            ], capture_output=True, text=True)
            
            print(result.stdout)
            if result.stderr:
                print("错误输出:")
                print(result.stderr)
            
            return result.returncode == 0
        except Exception as e:
            print(f"❌ 运行测试失败: {e}")
            return False


def generate_data(num_theorems: int = 100):
    """生成示例数据"""
    print(f"📊 生成 {num_theorems} 个定理的示例数据...")
    
    from src.data.lean_simulator import LeanDataSimulator
    from src.utils.helpers import ensure_directory
    
    # 确保数据目录存在
    ensure_directory("data")
    
    # 生成数据
    simulator = LeanDataSimulator()
    theorems = simulator.generate_dataset(num_theorems)
    
    # 保存数据
    output_file = "data/generated_theorems.json"
    simulator.save_dataset(theorems, output_file)
    
    print(f"✅ 数据已保存到: {output_file}")
    print(f"生成的定理统计:")
    
    # 统计信息
    categories = {}
    difficulties = {}
    
    for theorem in theorems:
        categories[theorem.category] = categories.get(theorem.category, 0) + 1
        difficulties[theorem.difficulty] = difficulties.get(theorem.difficulty, 0) + 1
    
    print("按类别分布:")
    for category, count in sorted(categories.items()):
        print(f"  {category}: {count}")
    
    print("按难度分布:")
    for difficulty, count in sorted(difficulties.items()):
        print(f"  难度{difficulty}: {count}")


def benchmark():
    """运行基准测试"""
    print("📈 运行基准测试...")
    
    from src.data.lean_simulator import LeanDataSimulator
    from src.models.llm_interface import create_llm_manager
    from src.energy.energy_constructor import EnergyFunctionConstructor
    from src.energy.energy_evaluator import EnergyFunctionEvaluator
    
    # 生成测试数据
    simulator = LeanDataSimulator()
    theorems = simulator.generate_dataset(10)
    
    # 创建组件
    llm_manager = create_llm_manager()
    constructor = EnergyFunctionConstructor(llm_manager)
    evaluator = EnergyFunctionEvaluator()
    
    # 运行基准测试
    print("正在运行基准测试...")
    results = evaluator.benchmark_constructor(constructor, theorems, num_samples=5)
    
    print("基准测试结果:")
    for key, value in results.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.4f}")
        else:
            print(f"  {key}: {value}")


def interactive_mode():
    """交互模式"""
    print("🎮 进入交互模式...")
    print("可用命令: demo, test, generate, benchmark, config, quit")
    
    config_manager = ConfigManager()
    
    while True:
        try:
            command = input("\n> ").strip().lower()
            
            if command == "quit" or command == "exit":
                print("👋 再见!")
                break
            elif command == "demo":
                run_demo()
            elif command == "test":
                run_tests()
            elif command == "generate":
                try:
                    num = int(input("输入要生成的定理数量 (默认100): ") or "100")
                    generate_data(num)
                except ValueError:
                    print("❌ 请输入有效数字")
            elif command == "benchmark":
                benchmark()
            elif command == "config":
                print("当前配置:")
                print(f"  LLM提供商: {config_manager.get('llm.provider', 'mock')}")
                print(f"  模型: {config_manager.get('llm.model', 'mock-gpt')}")
                print(f"  最大定理数: {config_manager.get('data.max_theorems', 100)}")
            elif command == "help":
                print("可用命令:")
                print("  demo - 运行基础演示")
                print("  test - 运行测试")
                print("  generate - 生成示例数据")
                print("  benchmark - 运行基准测试")
                print("  config - 显示配置")
                print("  quit - 退出")
            else:
                print(f"❌ 未知命令: {command}. 输入 'help' 查看帮助")
                
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Lean Tactic能量函数项目",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py demo                    # 运行基础演示
  python main.py test                    # 运行测试
  python main.py generate --num 50       # 生成50个定理
  python main.py benchmark               # 运行基准测试
  python main.py interactive             # 进入交互模式
        """
    )
    
    parser.add_argument(
        "command",
        choices=["demo", "test", "generate", "benchmark", "interactive"],
        help="要执行的命令"
    )
    
    parser.add_argument(
        "--num", "-n",
        type=int,
        default=100,
        help="生成数据时的定理数量 (默认: 100)"
    )
    
    parser.add_argument(
        "--config", "-c",
        default="config.yaml",
        help="配置文件路径 (默认: config.yaml)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日志级别 (默认: INFO)"
    )
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    
    # 加载配置
    config = load_config(args.config)
    
    # 执行命令
    if args.command == "demo":
        run_demo()
    elif args.command == "test":
        success = run_tests()
        sys.exit(0 if success else 1)
    elif args.command == "generate":
        generate_data(args.num)
    elif args.command == "benchmark":
        benchmark()
    elif args.command == "interactive":
        interactive_mode()


if __name__ == "__main__":
    main()
