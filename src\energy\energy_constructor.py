"""
能量函数构造器 - 基于大模型输出构造tactic选择的能量函数
"""

from typing import List, Dict, Any, Callable, Optional, Tuple
import re
import ast
import numpy as np
from dataclasses import dataclass
import inspect
import traceback

from ..models.llm_interface import <PERSON>MMana<PERSON>, LLMResponse
from ..tactics.tactic_types import TacticCandidate, TacticContext
from ..data.lean_simulator import ProofState


@dataclass
class EnergyFunctionSpec:
    """能量函数规格说明"""
    name: str
    description: str
    code: str
    parameters: Dict[str, Any]
    complexity_score: float  # 函数复杂度评分
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "description": self.description,
            "code": self.code,
            "parameters": self.parameters,
            "complexity_score": self.complexity_score
        }


class EnergyFunction:
    """能量函数的可执行包装器"""
    
    def __init__(self, spec: EnergyFunctionSpec):
        self.spec = spec
        self.function = None
        self.is_valid = False
        self._compile_function()
    
    def _compile_function(self):
        """编译能量函数代码"""
        try:
            # 创建安全的执行环境
            safe_globals = {
                '__builtins__': {
                    'len': len,
                    'max': max,
                    'min': min,
                    'abs': abs,
                    'sum': sum,
                    'any': any,
                    'all': all,
                    'float': float,
                    'int': int,
                    'str': str,
                    'list': list,
                    'dict': dict,
                },
                'np': np,
                'math': __import__('math'),
            }
            
            # 执行代码
            exec(self.spec.code, safe_globals)
            
            # 查找能量函数
            for name, obj in safe_globals.items():
                if callable(obj) and not name.startswith('_') and name not in ['np', 'math']:
                    self.function = obj
                    self.is_valid = True
                    break
            
            if not self.function:
                raise ValueError("No callable function found in the code")
                
        except Exception as e:
            print(f"Error compiling energy function: {e}")
            self.is_valid = False
    
    def evaluate(self, state: ProofState, tactic: TacticCandidate, context: TacticContext) -> float:
        """评估能量值"""
        if not self.is_valid:
            return float('inf')  # 无效函数返回无穷大能量
        
        try:
            # 准备函数参数
            args = self._prepare_arguments(state, tactic, context)
            
            # 调用函数
            energy = self.function(**args)
            
            # 确保返回值是有效的浮点数
            if isinstance(energy, (int, float)) and not np.isnan(energy):
                return float(energy)
            else:
                return float('inf')
                
        except Exception as e:
            print(f"Error evaluating energy function: {e}")
            return float('inf')
    
    def _prepare_arguments(self, state: ProofState, tactic: TacticCandidate, context: TacticContext) -> Dict[str, Any]:
        """准备函数参数"""
        # 获取函数签名
        sig = inspect.signature(self.function)
        args = {}

        # 创建一个简化的tactic对象，包含name属性以兼容旧代码
        class TacticWrapper:
            def __init__(self, candidate):
                self.name = candidate.tactic_name
                self.tactic_name = candidate.tactic_name
                self.arguments = candidate.arguments
                self.confidence = candidate.confidence

        tactic_wrapper = TacticWrapper(tactic)

        for param_name in sig.parameters:
            if param_name == 'state':
                args[param_name] = state
            elif param_name == 'tactic':
                args[param_name] = tactic_wrapper
            elif param_name == 'context':
                args[param_name] = context
            elif param_name == 'goals':
                args[param_name] = state.goals
            elif param_name == 'hypotheses':
                args[param_name] = state.hypotheses
            elif param_name == 'tactic_name':
                args[param_name] = tactic.tactic_name
            elif param_name == 'arguments':
                args[param_name] = tactic.arguments
            # 可以根据需要添加更多参数映射

        return args


class EnergyFunctionConstructor:
    """能量函数构造器"""
    
    def __init__(self, llm_manager: LLMManager):
        self.llm_manager = llm_manager
        self.function_cache: Dict[str, EnergyFunction] = {}
    
    def construct_energy_function(
        self, 
        proof_state: ProofState, 
        available_tactics: List[TacticCandidate],
        context: TacticContext,
        function_name: Optional[str] = None
    ) -> EnergyFunction:
        """构造能量函数"""
        
        # 创建缓存键
        cache_key = self._create_cache_key(proof_state, available_tactics, context)
        
        if cache_key in self.function_cache:
            return self.function_cache[cache_key]
        
        # 准备LLM输入
        state_str = self._format_proof_state(proof_state)
        tactics_str = self._format_tactics(available_tactics)
        context_str = self._format_context(context)
        
        # 调用LLM生成能量函数
        response = self.llm_manager.generate_energy_function(
            state_str, [t.tactic_name for t in available_tactics], context_str
        )
        
        if not response.success:
            # 如果LLM调用失败，返回默认能量函数
            return self._create_default_energy_function()
        
        # 解析LLM响应
        spec = self._parse_llm_response(response, function_name)
        
        # 创建能量函数
        energy_func = EnergyFunction(spec)
        
        # 缓存结果
        self.function_cache[cache_key] = energy_func
        
        return energy_func
    
    def _create_cache_key(self, state: ProofState, tactics: List[TacticCandidate], context: TacticContext) -> str:
        """创建缓存键"""
        state_hash = hash(str(state.goals) + str(state.hypotheses))
        tactics_hash = hash(tuple(t.tactic_name for t in tactics))
        context_hash = hash(str(context.goal_type) + str(context.proof_depth))
        return f"{state_hash}_{tactics_hash}_{context_hash}"
    
    def _format_proof_state(self, state: ProofState) -> str:
        """格式化证明状态"""
        return f"""
Goals: {state.goals}
Hypotheses: {state.hypotheses}
Context: {state.context}
"""
    
    def _format_tactics(self, tactics: List[TacticCandidate]) -> str:
        """格式化tactics"""
        tactic_strs = []
        for tactic in tactics:
            args_str = f"({', '.join(tactic.arguments)})" if tactic.arguments else ""
            tactic_strs.append(f"{tactic.tactic_name}{args_str}")
        return ", ".join(tactic_strs)
    
    def _format_context(self, context: TacticContext) -> str:
        """格式化上下文"""
        return f"""
Goal Type: {context.goal_type}
Available Hypotheses: {context.available_hypotheses}
Available Variables: {context.available_variables}
Proof Depth: {context.proof_depth}
Previous Tactics: {context.previous_tactics}
"""
    
    def _parse_llm_response(self, response: LLMResponse, function_name: Optional[str] = None) -> EnergyFunctionSpec:
        """解析LLM响应，提取能量函数代码"""
        content = response.content
        
        # 提取Python代码块
        code_blocks = re.findall(r'```python\n(.*?)\n```', content, re.DOTALL)
        
        if not code_blocks:
            # 如果没有找到代码块，尝试提取函数定义
            func_matches = re.findall(r'def\s+\w+.*?(?=\n\n|\n$|\Z)', content, re.DOTALL)
            if func_matches:
                code = func_matches[0]
            else:
                # 使用默认代码
                code = self._get_default_energy_code()
        else:
            code = code_blocks[0]
        
        # 提取描述
        description_match = re.search(r'(?:This|The)\s+energy\s+function\s+(.*?)(?:\.|$)', content, re.IGNORECASE)
        description = description_match.group(1) if description_match else "LLM generated energy function"
        
        # 计算复杂度评分
        complexity = self._calculate_complexity(code)
        
        name = function_name or "llm_energy_function"
        
        return EnergyFunctionSpec(
            name=name,
            description=description,
            code=code,
            parameters={},
            complexity_score=complexity
        )
    
    def _calculate_complexity(self, code: str) -> float:
        """计算代码复杂度"""
        try:
            tree = ast.parse(code)
            complexity = 0
            
            for node in ast.walk(tree):
                if isinstance(node, (ast.If, ast.For, ast.While)):
                    complexity += 1
                elif isinstance(node, ast.FunctionDef):
                    complexity += len(node.args.args)
            
            return float(complexity)
        except:
            return 1.0
    
    def _get_default_energy_code(self) -> str:
        """获取默认能量函数代码"""
        return """
def energy_function(state, tactic, context):
    \"\"\"Default energy function\"\"\"
    energy = 0.5  # Base energy
    
    # Prefer tactics that reduce goal count
    if len(state.goals) > 0:
        energy += len(state.goals) * 0.1
    
    # Adjust based on tactic type
    if tactic.tactic_name == "simp":
        energy += 0.2
    elif tactic.tactic_name == "rw":
        energy += 0.1
    elif tactic.tactic_name == "apply":
        energy += 0.3
    
    return energy
"""
    
    def _create_default_energy_function(self) -> EnergyFunction:
        """创建默认能量函数"""
        spec = EnergyFunctionSpec(
            name="default_energy",
            description="Default energy function",
            code=self._get_default_energy_code(),
            parameters={},
            complexity_score=1.0
        )
        return EnergyFunction(spec)
    
    def evaluate_tactics(
        self, 
        state: ProofState, 
        tactics: List[TacticCandidate], 
        context: TacticContext
    ) -> List[Tuple[TacticCandidate, float]]:
        """评估一组tactics的能量值"""
        
        # 构造能量函数
        energy_func = self.construct_energy_function(state, tactics, context)
        
        # 评估每个tactic
        results = []
        for tactic in tactics:
            energy = energy_func.evaluate(state, tactic, context)
            results.append((tactic, energy))
        
        # 按能量值排序（低能量优先）
        results.sort(key=lambda x: x[1])
        
        return results
    
    def clear_cache(self):
        """清空缓存"""
        self.function_cache.clear()
